"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/create/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx":
/*!***********************************************************************!*\
  !*** ./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx ***!
  \***********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _add_crew_member_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../add-crew-member-dialog */ \"(app-pages-browser)/./src/app/ui/crew/add-crew-member-dialog.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/queries.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst CrewMultiSelectDropdown = (param)=>{\n    let { value = [], onChange, memberIdOptions = [], departments = [], filterByAdmin = false, offline = false, vesselID = 0 } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [crewList, setCrewList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openCreateMemberDialog, setOpenCreateMemberDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIDs, setSelectedIDs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const seaLogsMemberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_4__[\"default\"]();\n    const handleSetCrewList = (crewListRaw)=>{\n        // If vesselID > 0, filter the crew list to display only the vessel crew.\n        const vesselCrewList = vesselID > 0 ? crewListRaw.filter((crew)=>crew.vehicles.nodes.some((vehicle)=>+vehicle.id === vesselID)) : crewListRaw;\n        const createOption = {\n            value: \"newCrewMember\",\n            label: \"--- Create Crew Member ---\"\n        };\n        const data = vesselCrewList.filter((crew)=>filterByAdmin ? !crewIsAdmin(crew) : true);\n        if (departments.length > 0) {\n            const departmentList = departments.flatMap((department)=>{\n                return department.id;\n            });\n            const crews = data.filter((crew)=>crew.departments.nodes.some((node)=>departmentList.includes(node.id))).map((item)=>{\n                var _item_firstName, _item_surname;\n                return {\n                    value: item.id,\n                    label: \"\".concat((_item_firstName = item.firstName) !== null && _item_firstName !== void 0 ? _item_firstName : \"\", \" \").concat((_item_surname = item.surname) !== null && _item_surname !== void 0 ? _item_surname : \"\")\n                };\n            });\n            if (memberIdOptions.length === 0) {\n                setCrewList([\n                    createOption,\n                    ...crews\n                ]);\n            } else {\n                const filteredCrewList = crews.filter((crew)=>{\n                    return memberIdOptions.includes(crew.value);\n                });\n                setCrewList(filteredCrewList);\n            }\n        } else {\n            const crews = data.map((item)=>{\n                var _item_firstName, _item_surname;\n                return {\n                    value: item.id,\n                    label: \"\".concat((_item_firstName = item.firstName) !== null && _item_firstName !== void 0 ? _item_firstName : \"\", \" \").concat((_item_surname = item.surname) !== null && _item_surname !== void 0 ? _item_surname : \"\")\n                };\n            });\n            if (memberIdOptions.length === 0) {\n                setCrewList([\n                    createOption,\n                    ...crews\n                ]);\n            } else {\n                const filteredCrewList = crews.filter((crew)=>{\n                    return memberIdOptions.includes(crew.value);\n                });\n                setCrewList(filteredCrewList);\n            }\n        }\n    };\n    const [querySeaLogsMembersList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_9__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_8__.ReadSeaLogsMembers, {\n        fetchPolicy: \"cache-and-network\",\n        onError: (error)=>{\n            console.error(\"querySeaLogsMembersList error\", error);\n        }\n    });\n    const loadCrewMembers = async ()=>{\n        let allMembers = [];\n        let offset = 0;\n        const limit = 100;\n        let hasNextPage = true;\n        try {\n            while(hasNextPage){\n                var _response_data;\n                const response = await querySeaLogsMembersList({\n                    variables: {\n                        filter: {\n                            isArchived: {\n                                eq: false\n                            }\n                        },\n                        limit: limit,\n                        offset: offset\n                    }\n                });\n                if ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.readSeaLogsMembers) {\n                    const data = response.data.readSeaLogsMembers.nodes;\n                    const pageInfo = response.data.readSeaLogsMembers.pageInfo;\n                    if (data && data.length > 0) {\n                        allMembers = [\n                            ...allMembers,\n                            ...data\n                        ];\n                    }\n                    hasNextPage = (pageInfo === null || pageInfo === void 0 ? void 0 : pageInfo.hasNextPage) || false;\n                    offset += limit;\n                } else {\n                    hasNextPage = false;\n                }\n            }\n            // Set all collected members at once\n            if (allMembers.length > 0) {\n                handleSetCrewList(allMembers);\n            }\n        } catch (error) {\n            console.error(\"Error loading all crew members:\", error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading && !offline) {\n            loadCrewMembers();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading,\n        offline\n    ]);\n    // if (!offline) {\n    // getSeaLogsMembersList(handleSetCrewList)\n    // }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            seaLogsMemberModel.getAll().then((data)=>{\n                handleSetCrewList(data);\n            });\n        }\n    }, [\n        offline\n    ]);\n    const crewIsAdmin = (crew)=>{\n        var _crew_groups_nodes;\n        return ((_crew_groups_nodes = crew.groups.nodes) === null || _crew_groups_nodes === void 0 ? void 0 : _crew_groups_nodes.filter((permission)=>{\n            return permission.code === \"admin\";\n        }).length) > 0;\n    };\n    const handleOnChange = (value)=>{\n        if (!value) {\n            setSelectedIDs([]);\n            onChange([]);\n            return;\n        }\n        const valueArray = Array.isArray(value) ? value : [\n            value\n        ];\n        // Fix the condition to properly check for 'newCrewMember'\n        if (valueArray.find((option)=>option.value === \"newCrewMember\")) {\n            setOpenCreateMemberDialog(true);\n            return;\n        }\n        if (valueArray.length === 0) {\n            setSelectedIDs([]);\n            onChange([]);\n            return;\n        }\n        // Ensure we're working with valid Option objects\n        const validOptions = valueArray.filter((option)=>option && typeof option === \"object\");\n        setSelectedIDs(validOptions);\n        onChange(validOptions);\n    };\n    const [queryAddMember] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_10__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CREATE_USER, {\n        fetchPolicy: \"no-cache\",\n        onCompleted: (response)=>{\n            const data = response.createSeaLogsMember;\n            if (data.id > 0) {\n                setOpenCreateMemberDialog(false);\n                const newData = {\n                    value: data.id,\n                    label: data.firstName + \" \" + data.surname\n                };\n                setCrewList([\n                    ...crewList,\n                    newData\n                ]);\n                setSelectedIDs([\n                    ...selectedIDs,\n                    data.id\n                ]);\n                onChange([\n                    newData,\n                    ...value.map((id)=>{\n                        const crew = crewList.find((c)=>c.value === id);\n                        return crew;\n                    })\n                ]);\n                setError(false);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"createUser error\", error.message);\n            setError(error);\n        }\n    });\n    const handleAddNewMember = async ()=>{\n        const variables = {\n            input: {\n                firstName: document.getElementById(\"crew-firstName\").value ? document.getElementById(\"crew-firstName\").value : null,\n                surname: document.getElementById(\"crew-surname\").value ? document.getElementById(\"crew-surname\").value : null,\n                email: document.getElementById(\"crew-email\").value ? document.getElementById(\"crew-email\").value : null,\n                phoneNumber: document.getElementById(\"crew-phoneNumber\").value ? document.getElementById(\"crew-phoneNumber\").value : null,\n                username: document.getElementById(\"crew-username\").value ? document.getElementById(\"crew-username\").value : null,\n                password: document.getElementById(\"crew-password\").value ? document.getElementById(\"crew-password\").value : null\n            }\n        };\n        if (offline) {\n            // queryAddMember\n            const data = await seaLogsMemberModel.save({\n                ...variables.input,\n                id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_5__.generateUniqueId)()\n            });\n            setOpenCreateMemberDialog(false);\n            const newData = {\n                value: data.id,\n                label: data.firstName + \" \" + data.surname\n            };\n            setCrewList([\n                ...crewList,\n                newData\n            ]);\n            setSelectedIDs([\n                ...selectedIDs,\n                data.id\n            ]);\n            onChange([\n                newData,\n                ...value.map((id)=>{\n                    const crew = crewList.find((c)=>c.value === id);\n                    return crew;\n                })\n            ]);\n            setError(false);\n        } else {\n            await queryAddMember({\n                variables: variables\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDE80 CrewMultiSelectDropdown useEffect triggered\");\n        console.log(\"\\uD83D\\uDE80 value prop:\", value);\n        console.log(\"\\uD83D\\uDE80 crewList length:\", crewList === null || crewList === void 0 ? void 0 : crewList.length);\n        console.log(\"\\uD83D\\uDE80 current selectedIDs:\", selectedIDs);\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(crewList)) {\n            if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_2___default()(value)) {\n                // If value is empty, clear the selection\n                console.log(\"\\uD83D\\uDE80 Clearing selection - value is empty\");\n                setSelectedIDs([]);\n            } else {\n                // Convert value array to proper Option objects\n                const selectedOptions = value.map((id)=>{\n                    // Handle both string and number IDs\n                    const numericId = typeof id === \"string\" ? parseInt(id, 10) : id;\n                    const option = crewList.find((crew)=>crew.value === numericId || crew.value === id);\n                    if (option) {\n                        console.log(\"\\uD83D\\uDE80 Found option for ID\", id, \":\", option);\n                        return option;\n                    }\n                    // Debug: Let's see what's in the crewList\n                    console.log(\"\\uD83D\\uDE80 Looking for ID:\", id, \"type:\", typeof id);\n                    console.log(\"\\uD83D\\uDE80 Looking for numericId:\", numericId, \"type:\", typeof numericId);\n                    console.log(\"\\uD83D\\uDE80 First few crew values:\", crewList.slice(0, 3).map((c)=>({\n                            value: c.value,\n                            type: typeof c.value\n                        })));\n                    // If not found in crew list, create a placeholder option\n                    console.log(\"\\uD83D\\uDE80 Creating placeholder for ID\", id);\n                    return {\n                        value: numericId,\n                        label: \"Member \".concat(numericId)\n                    };\n                }).filter((option)=>option !== null);\n                console.log(\"\\uD83D\\uDE80 Setting selectedOptions:\", selectedOptions);\n                setSelectedIDs(selectedOptions);\n            }\n        } else {\n            console.log(\"\\uD83D\\uDE80 CrewList is empty, not processing\");\n        }\n    }, [\n        value,\n        crewList\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_6__.Combobox, {\n                options: crewList,\n                value: selectedIDs,\n                onChange: handleOnChange,\n                placeholder: \"Select Crew\",\n                multi: true,\n                isLoading: !crewList\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\multiselect-dropdown\\\\multiselect-dropdown.tsx\",\n                lineNumber: 389,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_add_crew_member_dialog__WEBPACK_IMPORTED_MODULE_7__.AddCrewMemberDialog, {\n                openDialog: openCreateMemberDialog,\n                setOpenDialog: setOpenCreateMemberDialog,\n                handleCreate: handleAddNewMember,\n                actionText: \"Add Crew Member\",\n                error: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\multiselect-dropdown\\\\multiselect-dropdown.tsx\",\n                lineNumber: 397,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CrewMultiSelectDropdown, \"B5kTBW1ijKbUdQ1QBKs5KkaV5Fg=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_9__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_10__.useMutation\n    ];\n});\n_c = CrewMultiSelectDropdown;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewMultiSelectDropdown);\nvar _c;\n$RefreshReg$(_c, \"CrewMultiSelectDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx\n"));

/***/ })

});